@echo off
chcp 65001 >nul
echo === Background Upgrade Service Check ===
echo Time: %date% %time%
echo.

echo 1. Check if app is installed:
adb shell pm list packages | findstr com.seres.background.upgrade
if %errorlevel% equ 0 (
    echo    [OK] App is installed
) else (
    echo    [ERROR] App is not installed
    echo    Please install: adb install UDiskBackgroundUpgradeService-debug.apk
    pause
    exit /b 1
)
echo.

echo 2. Check service process:
adb shell ps | findstr background.upgrade
if %errorlevel% equ 0 (
    echo    [OK] Service process is running
) else (
    echo    [WARNING] Service process not found
    echo    Trying to start service...
    adb shell am startservice com.seres.background.upgrade/.service.UDiskBackgroundUpgradeService
    timeout /t 3 >nul
    adb shell ps | findstr background.upgrade
    if %errorlevel% equ 0 (
        echo    [OK] Service started successfully
    ) else (
        echo    [ERROR] Failed to start service
    )
)
echo.

echo 3. Check service status:
adb shell dumpsys activity services | findstr BackgroundUpgrade
if %errorlevel% equ 0 (
    echo    [OK] Service is registered
) else (
    echo    [WARNING] Service not found in system
)
echo.

echo 4. Check recent logs:
echo    Recent logs (last 5 lines):
adb logcat -d | findstr BackgroundUpgrade | more +1
echo.

echo 5. Check notifications:
adb shell dumpsys notification | findstr background.upgrade
if %errorlevel% equ 0 (
    echo    [OK] Found service notifications
) else (
    echo    [INFO] No notifications found
)
echo.

echo 6. Check permissions:
echo    Checking key permissions:
adb shell dumpsys package com.seres.background.upgrade | findstr "BOOT_COMPLETED.*granted=true"
if %errorlevel% equ 0 (
    echo    [OK] BOOT_COMPLETED permission granted
) else (
    echo    [WARNING] BOOT_COMPLETED permission not granted
)

adb shell dumpsys package com.seres.background.upgrade | findstr "FOREGROUND_SERVICE.*granted=true"
if %errorlevel% equ 0 (
    echo    [OK] FOREGROUND_SERVICE permission granted
) else (
    echo    [WARNING] FOREGROUND_SERVICE permission not granted
)
echo.

echo 7. Check upgrade task JSON files:
echo    Latest upgrade task JSON:
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/latest_upgrade_task.json 2>nul
if %errorlevel% equ 0 (
    echo    [OK] Found latest upgrade task JSON
) else (
    echo    [INFO] No upgrade task JSON found
)
echo.
echo    All upgrade task JSON files:
adb shell ls -la /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/upgrade_task_*.json 2>nul
echo.

echo 8. Try to start debug activity:
echo    Starting debug activity (click anywhere to close)...
adb shell am start -n com.seres.background.upgrade/.DebugActivity
echo.

echo === Manual Commands ===
echo To monitor logs: adb logcat ^| findstr BackgroundUpgrade
echo To check process: adb shell ps ^| findstr background.upgrade
echo To start service: adb shell am startservice com.seres.background.upgrade/.service.UDiskBackgroundUpgradeService
echo To restart app: adb shell am force-stop com.seres.background.upgrade ^&^& adb shell am startservice com.seres.background.upgrade/.service.UDiskBackgroundUpgradeService
echo.

echo Check completed!
pause
