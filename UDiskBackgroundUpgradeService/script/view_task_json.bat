@echo off
chcp 65001 >nul
echo === View Upgrade Task JSON ===
echo.

echo 1. Check status directory...
adb shell ls -la /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/
echo.

echo 2. Latest upgrade task JSON:
echo =====================================
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/latest_upgrade_task.json 2>nul
if %errorlevel% neq 0 (
    echo [INFO] No latest upgrade task JSON found
)
echo =====================================
echo.

echo 3. All upgrade task JSON files:
adb shell ls -1 /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/upgrade_task_*.json 2>nul
if %errorlevel% equ 0 (
    echo.
    echo Select a specific task file to view (or press Enter to skip):
    set /p taskFile="Enter filename: "
    if not "!taskFile!"=="" (
        echo.
        echo Content of !taskFile!:
        echo =====================================
        adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/!taskFile!
        echo =====================================
    )
) else (
    echo [INFO] No upgrade task JSON files found
)
echo.

echo 4. Task status:
echo =====================================
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/task_status.json 2>nul
if %errorlevel% neq 0 (
    echo [INFO] No task status found
)
echo =====================================
echo.

echo 5. USB status:
echo =====================================
adb shell cat /storage/emulated/0/Android/data/com.seres.background.upgrade/files/status/usb_status.json 2>nul
if %errorlevel% neq 0 (
    echo [INFO] No USB status found
)
echo =====================================
echo.

echo 6. Check /ota_share/ directory:
echo =====================================
adb shell ls -la /ota_share/ 2>nul
if %errorlevel% neq 0 (
    echo [INFO] /ota_share/ directory not found or empty
)
echo =====================================
echo.

echo View completed!
pause
