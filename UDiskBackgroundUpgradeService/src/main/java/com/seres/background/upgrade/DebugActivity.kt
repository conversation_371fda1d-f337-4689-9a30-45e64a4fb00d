package com.seres.background.upgrade

import android.app.Activity
import android.os.Bundle
import android.view.MotionEvent
import android.widget.ScrollView
import android.widget.TextView
import com.seres.background.upgrade.utils.TestUtils

/**
 * 调试Activity
 * 隐藏的调试界面，用于查看服务状态
 * 通过ADB命令启动: adb shell am start -n com.seres.background.upgrade/.DebugActivity
 * 点击任意地方关闭
 */
class DebugActivity : Activity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 创建简单的文本视图显示调试信息
        val textView = TextView(this).apply {
            textSize = 10f
            setPadding(16, 16, 16, 16)
            text = "正在加载调试信息...\n\n点击任意地方关闭此窗口"
            setTextIsSelectable(true) // 允许选择文本
        }

        // 创建滚动视图，支持长内容滚动
        val scrollView = ScrollView(this).apply {
            addView(textView)
            setPadding(8, 8, 8, 8)
        }

        setContentView(scrollView)

        // 异步加载调试信息
        Thread {
            val debugInfo = TestUtils.getDebugInfo(this@DebugActivity)
            runOnUiThread {
                textView.text = "$debugInfo\n\n点击任意地方关闭此窗口"
            }
        }.start()
    }

    /**
     * 重写触摸事件，点击任意地方关闭
     */
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event?.action == MotionEvent.ACTION_DOWN) {
            finish()
            return true
        }
        return super.onTouchEvent(event)
    }

    /**
     * 重写返回键，直接关闭
     */
    override fun onBackPressed() {
        finish()
    }
}
