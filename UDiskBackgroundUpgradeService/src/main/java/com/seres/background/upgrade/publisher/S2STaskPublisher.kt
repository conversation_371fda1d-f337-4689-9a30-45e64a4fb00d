package com.seres.background.upgrade.publisher

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import seres.s2s.internal.IAsyncResultCallback
import seres.s2s.internal.IS2SReportListener
import seres.s2s.internal.IS2SService
import com.seres.background.upgrade.model.InventoryInfo
import com.seres.background.upgrade.utils.LogUtils
import java.util.concurrent.Executors

/**
 * S2S任务发布者
 * 负责通过S2S服务发布升级任务和接收资产信息
 */
class S2STaskPublisher(private val context: Context) {
    
    private val TAG = "S2STaskPublisher"
    private val threadPool = Executors.newCachedThreadPool()
    private val mainHandler = Handler(Looper.getMainLooper()) // 主线程Handler用于重试绑定
    
    // 服务绑定重试参数
    private val MAX_RETRY_ATTEMPTS = 20
    private val RETRY_DELAY_MS = 3000L
    private var retryAttempts = 0
    
    // S2S服务相关
    private var s2sService: IS2SService? = null
    private var isServiceConnected = false
    
    // 回调接口
    private var versionCompatibilityChecker: VersionCompatibilityChecker? = null
    
    // 升级任务相关常量
    companion object {
        const val UPGRADE_TASK_NOTIFY_HASH = -320322560 // UDiskSrv_UpgradeTaskNotify_OTAM_HASH
        const val UPGRADE_TASK_MATCH_HASH = 1137060134 // UDiskSrv_UpgradeTaskIsMatch_OTAM_HASH
        const val INVENTORY_INFO_SIGNAL_HASH = 1001 // 资产信息信号Hash (需要根据实际IDL确定)
        const val UPGRADE_APP_ID = 999 // 升级服务的App ID
        const val S2S_SERVICE_PACKAGE = "com.seres.dds" // S2S服务包名
        const val S2S_SERVICE_CLASS = "com.seres.dds.server.IpcServer" // S2S服务实现类名
    }
    
    /**
     * 版本兼容性检查器接口
     */
    interface VersionCompatibilityChecker {
        fun checkCompatibility(inventoryInfoList: List<InventoryInfo>, taskId: String): Boolean
    }
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            s2sService = IS2SService.Stub.asInterface(service)
            isServiceConnected = true
            retryAttempts = 0 // 连接成功后重置重试计数器
            LogUtils.i(TAG, "已连接到S2S服务")
            
            // 注册资产信息监听器
            registerInventoryInfoListener()
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            s2sService = null
            isServiceConnected = false
            LogUtils.w(TAG, "与S2S服务断开连接，准备重连")
            scheduleReconnect() // 服务断开时触发重连
        }

        override fun onBindingDied(name: ComponentName?) {
            super.onBindingDied(name)
            s2sService = null
            isServiceConnected = false
            LogUtils.w(TAG, "S2S服务绑定已失效，准备重连")
            scheduleReconnect()
        }
    }
    
    /**
     * 资产信息监听器
     */
    private val inventoryInfoListener = object : IS2SReportListener.Stub() {
        override fun notify(data: Bundle?) {
            data?.let { handleInventoryInfoNotification(it) }
        }
    }
    
    init {
        connectToS2SService()
        LogUtils.i(TAG, "S2STaskPublisher初始化完成")
    }
    
    /**
     * 设置版本兼容性检查器
     */
    fun setVersionCompatibilityChecker(checker: VersionCompatibilityChecker) {
        this.versionCompatibilityChecker = checker
    }
    
    /**
     * 连接到S2S服务（使用精确的ComponentName定位服务）
     */
    private fun connectToS2SService() {
        try {
            val intent = Intent().apply {
                component = ComponentName(S2S_SERVICE_PACKAGE, S2S_SERVICE_CLASS)
            }
            
            val bound = context.bindService(
                intent, 
                serviceConnection, 
                Context.BIND_AUTO_CREATE or Context.BIND_ABOVE_CLIENT
            )
            
            if (bound) {
                LogUtils.i(TAG, "S2S服务绑定请求已发送")
            } else {
                LogUtils.e(TAG, "S2S服务绑定请求失败，准备重试")
                scheduleReconnect()
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "连接S2S服务时出错: ${e.message}", e)
            scheduleReconnect()
        }
    }
    
    /**
     * 调度服务重连
     */
    private fun scheduleReconnect() {
        if (retryAttempts < MAX_RETRY_ATTEMPTS) {
            retryAttempts++
            val delay = RETRY_DELAY_MS * retryAttempts // 指数退避策略
            mainHandler.postDelayed({
                LogUtils.i(TAG, "第${retryAttempts}次重试连接S2S服务 (延迟${delay}ms)")
                connectToS2SService()
            }, delay)
        } else {
            LogUtils.e(TAG, "已达到最大重试次数(${MAX_RETRY_ATTEMPTS})，停止重试")
        }
    }
    
    /**
     * 注册资产信息监听器
     */
    private fun registerInventoryInfoListener() {
        try {
            if (isServiceConnected) {
                val signalHashIds = intArrayOf(INVENTORY_INFO_SIGNAL_HASH)
                s2sService?.registerS2SSignalListener(UPGRADE_APP_ID, inventoryInfoListener, signalHashIds)
                LogUtils.i(TAG, "已注册资产信息监听器")
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "注册资产信息监听器失败: ${e.message}", e)
        }
    }
    
    /**
     * 发布升级任务
     */
    fun publishUpgradeTask(taskId: String, taskJson: String, callback: (Boolean) -> Unit) {
        threadPool.submit {
            try {
                LogUtils.i(TAG, "发布升级任务: $taskId")
                
                if (!isServiceConnected) {
                    LogUtils.w(TAG, "S2S服务未连接，尝试重新连接")
                    connectToS2SService()
                    Thread.sleep(2000) // 等待连接
                }
                
                if (isServiceConnected) {
                    // 创建参数Bundle
                    val params = Bundle().apply {
                        putString("action", "UDiskSrv_UpgradeTaskNotify_OTAM")
                        putString("upgradetaskinfo", taskJson)
                        putString("taskId", taskId)
                        putLong("timestamp", System.currentTimeMillis())
                    }
                    
                    // 通过S2S服务发布任务
                    s2sService?.invokeAsync(UPGRADE_APP_ID, UPGRADE_TASK_NOTIFY_HASH, params,
                        object : IAsyncResultCallback.Stub() {
                            override fun onResult(bundle: Bundle?) {
                                val success = bundle?.getBoolean("success", false) ?: false
                                if (success) {
                                    LogUtils.i(TAG, "升级任务发布成功: $taskId")
                                } else {
                                    val error = bundle?.getString("error", "未知错误")
                                    LogUtils.e(TAG, "升级任务发布失败: $error")
                                }
                                callback(success)
                            }
                        })
                } else {
                    LogUtils.e(TAG, "无法发布任务: S2S服务未连接")
                    callback(false)
                }
                
            } catch (e: Exception) {
                LogUtils.e(TAG, "发布升级任务时出错: ${e.message}", e)
                callback(false)
            }
        }
    }
    
    /**
     * 通知升级任务匹配结果
     */
    fun notifyUpgradeTaskMatch(taskId: String, isMatch: Boolean) {
        threadPool.submit {
            try {
                LogUtils.i(TAG, "通知升级任务匹配结果: $taskId -> $isMatch")
                
                if (isServiceConnected) {
                    val params = Bundle().apply {
                        putString("action", "UDiskSrv_UpgradeTaskIsMatch_OTAM")
                        putBoolean("upgradetaskismatch", isMatch)
                        putString("taskId", taskId)
                        putLong("timestamp", System.currentTimeMillis())
                    }
                    
                    s2sService?.invokeAsync(UPGRADE_APP_ID, UPGRADE_TASK_MATCH_HASH, params,
                        object : IAsyncResultCallback.Stub() {
                            override fun onResult(bundle: Bundle?) {
                                val success = bundle?.getBoolean("success", false) ?: false
                                LogUtils.i(TAG, "升级任务匹配通知发送${if (success) "成功" else "失败"}: $taskId")
                            }
                        })
                } else {
                    LogUtils.e(TAG, "无法发送匹配通知: S2S服务未连接")
                }
                
            } catch (e: Exception) {
                LogUtils.e(TAG, "发送升级任务匹配通知时出错: ${e.message}", e)
            }
        }
    }
    
    /**
     * 处理资产信息通知
     */
    private fun handleInventoryInfoNotification(data: Bundle) {
        try {
            LogUtils.d(TAG, "收到资产信息通知")
            
            // 解析资产信息数据
            val inventoryInfoList = parseInventoryInfoFromBundle(data)
            val taskId = data.getString("taskId", "")
            
            if (inventoryInfoList.isNotEmpty() && taskId.isNotEmpty()) {
                // 检查版本兼容性
                val isCompatible = versionCompatibilityChecker?.checkCompatibility(inventoryInfoList, taskId) ?: false
                
                // 通知升级任务匹配结果
                notifyUpgradeTaskMatch(taskId, isCompatible)
                
                LogUtils.i(TAG, "版本兼容性检查完成: $taskId -> $isCompatible")
            } else {
                LogUtils.w(TAG, "资产信息数据不完整")
            }
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "处理资产信息通知时出错: ${e.message}", e)
        }
    }
    
    /**
     * 从Bundle中解析资产信息
     */
    private fun parseInventoryInfoFromBundle(data: Bundle): List<InventoryInfo> {
        val inventoryInfoList = mutableListOf<InventoryInfo>()
        
        try {
            // 这里需要根据实际的Bundle数据结构来解析
            // 假设资产信息以数组形式传递
            val inventoryArray = data.getStringArray("OTAM_notifyInventoryInfoStatus")
            
            inventoryArray?.forEach { inventoryJson ->
                // 解析每个资产信息JSON
                // 这里简化处理，实际需要根据具体数据格式解析
                val inventoryInfo = InventoryInfo(
                    ecuName = data.getString("ecuName", ""),
                    softwareVersion = data.getString("softwareVersion", ""),
                    partNumber = data.getString("partNumber", ""),
                    supplierCode = data.getString("supplierCode", ""),
                    serialNumber = data.getString("serialNumber", ""),
                    hardwareVersion = data.getString("hardwareVersion", ""),
                    bootloaderVersion = data.getString("bootloaderVersion", ""),
                    backupVersion = data.getString("backupVersion", "")
                )
                inventoryInfoList.add(inventoryInfo)
            }
            
        } catch (e: Exception) {
            LogUtils.e(TAG, "解析资产信息失败: ${e.message}", e)
        }
        
        return inventoryInfoList
    }
    
    /**
     * 检查S2S服务连接状态
     */
    fun isConnected(): Boolean = isServiceConnected
    
    /**
     * 重新连接S2S服务
     */
    fun reconnect() {
        if (!isServiceConnected) {
            retryAttempts = 0 // 重置重试计数器
            connectToS2SService()
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            if (isServiceConnected) {
                s2sService?.unregisterS2SSignalListener(UPGRADE_APP_ID)
                context.unbindService(serviceConnection)
                isServiceConnected = false
            }
            mainHandler.removeCallbacksAndMessages(null) // 清除所有未执行的重试任务
            threadPool.shutdown() // 关闭线程池
            LogUtils.i(TAG, "S2STaskPublisher清理完成")
        } catch (e: Exception) {
            LogUtils.e(TAG, "清理资源时出错: ${e.message}", e)
        }
    }
}
