package com.seres.background.upgrade.utils

import android.content.Context
import com.google.gson.Gson
import com.seres.background.upgrade.model.*
import com.seres.background.upgrade.monitor.StatusMonitor
import java.io.File

/**
 * 测试工具类
 * 用于生成测试数据和验证功能
 */
object TestUtils {
    
    private val gson = Gson()

    /**
     * 打印格式化的JSON
     */
    fun printFormattedJson(jsonString: String): String {
        return try {
            val jsonObject = gson.fromJson(jsonString, Any::class.java)
            gson.newBuilder().setPrettyPrinting().create().toJson(jsonObject)
        } catch (e: Exception) {
            jsonString
        }
    }

    /**
     * 获取调试信息
     */
    fun getDebugInfo(context: Context): String {
        val statusMonitor = StatusMonitor(context)
        val debugInfo = StringBuilder()

        debugInfo.append("=== 后台升级服务调试信息 ===\n")
        debugInfo.append("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())}\n")
        debugInfo.append("PID: ${android.os.Process.myPid()}\n\n")

        // 服务状态
        debugInfo.append("=== 服务状态 ===\n")
        val serviceStatus = statusMonitor.readServiceStatus()
        if (serviceStatus != null) {
            debugInfo.append("服务名: ${serviceStatus.serviceName}\n")
            debugInfo.append("运行状态: ${serviceStatus.isRunning}\n")
            debugInfo.append("启动时间: ${serviceStatus.startTime}\n")
            debugInfo.append("最后更新: ${serviceStatus.lastUpdateTime}\n")
            debugInfo.append("PID: ${serviceStatus.pid}\n")
        } else {
            debugInfo.append("未找到服务状态信息\n")
        }
        debugInfo.append("\n")

        // USB状态
        debugInfo.append("=== USB状态 ===\n")
        val usbStatus = statusMonitor.readUsbStatus()
        if (usbStatus != null) {
            debugInfo.append("动作: ${usbStatus.action}\n")
            debugInfo.append("设备路径: ${usbStatus.devicePath}\n")
            debugInfo.append("时间戳: ${usbStatus.timestamp}\n")
        } else {
            debugInfo.append("未找到USB状态信息\n")
        }
        debugInfo.append("\n")

        // 任务状态
        debugInfo.append("=== 任务状态 ===\n")
        val taskStatus = statusMonitor.readTaskStatus()
        if (taskStatus != null) {
            debugInfo.append("任务ID: ${taskStatus.taskId}\n")
            debugInfo.append("状态: ${taskStatus.status}\n")
            debugInfo.append("创建时间: ${taskStatus.createTime}\n")
            debugInfo.append("更新时间: ${taskStatus.updateTime}\n")
            debugInfo.append("USB路径: ${taskStatus.usbPath}\n")
            debugInfo.append("升级包数量: ${taskStatus.packageCount}\n")
            if (taskStatus.errorMessage.isNotEmpty()) {
                debugInfo.append("错误信息: ${taskStatus.errorMessage}\n")
            }
        } else {
            debugInfo.append("未找到任务状态信息\n")
        }
        debugInfo.append("\n")

        // 心跳检查
        debugInfo.append("=== 心跳检查 ===\n")
        val heartbeat = statusMonitor.checkHeartbeat()
        if (heartbeat != null) {
            debugInfo.append("心跳: $heartbeat\n")
        } else {
            debugInfo.append("未找到心跳信息\n")
        }
        debugInfo.append("\n")

        // 升级任务JSON
        debugInfo.append("=== 升级任务JSON ===\n")
        val latestTaskJson = statusMonitor.readLatestUpgradeTaskJson()
        if (latestTaskJson != null) {
            debugInfo.append("最新升级任务JSON:\n")
            debugInfo.append(printFormattedJson(latestTaskJson))
            debugInfo.append("\n")
        } else {
            debugInfo.append("未找到升级任务JSON\n")
        }

        return debugInfo.toString()
    }
}
